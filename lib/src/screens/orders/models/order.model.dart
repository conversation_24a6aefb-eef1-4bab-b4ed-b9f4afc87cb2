import 'package:equatable/equatable.dart';
import 'tracking.model.dart';
import 'station.model.dart';

class OrderModel extends Equatable {
  final int id;
  final String? barcode;
  final Station? fromStation;
  final Station? toStation;
  final String type;
  final String receiverName;
  final String receiverPhone;
  final String note;
  final String price;
  final String points;
  final String status;
  final String statusText;
  final TrackingModel? latestTracking;

  const OrderModel({
    required this.id,
    this.barcode,
    this.fromStation,
    this.toStation,
    this.type = '',
    this.receiverName = '',
    this.receiverPhone = '',
    this.points = '',
    this.note = '',
    this.price = '',
    this.status = '',
    this.statusText = '',
    this.latestTracking,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      barcode: json['barcode'],
      fromStation: json['from_station'] != null
          ? Station.fromJson(json['from_station'] as Map<String, dynamic>)
          : null,
      toStation: json['to_station'] != null
          ? Station.fromJson(json['to_station'] as Map<String, dynamic>)
          : null,
      type: json['type'] ?? '',
      points: json['points']?.toString() ?? '',
      receiverName: json['receiver_name'] ?? '',
      receiverPhone: json['receiver_phone'] ?? '',
      note: json['note'] ?? '',
      price: json['price'] ?? '',
      status: json['status'] ?? '',
      statusText: json['status_text'] ?? '',
      latestTracking: json['latest_tracking'] != null
          ? TrackingModel.fromJson(
              json['latest_tracking'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'barcode': barcode,
      'from_station': fromStation?.toJson(),
      'to_station': toStation?.toJson(),
      'type': type,
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'note': note,
      'price': price,
      'status': status,
      'status_text': statusText,
      'latest_tracking': latestTracking?.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        barcode,
        fromStation,
        toStation,
        type,
        receiverName,
        receiverPhone,
        note,
        price,
        status,
        statusText,
        latestTracking,
      ];
}
