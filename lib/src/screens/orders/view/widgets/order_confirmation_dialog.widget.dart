import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/models/type.model.dart';
import 'package:dropx/src/screens/orders/models/pricing.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../generated/assets.gen.dart';

class OrderConfirmationDialog extends StatelessWidget {
  final Station departureStation;
  final Station deliveryStation;
  final OrderType orderType;
  final String receiverName;
  final String receiverPhone;
  final String notes;
  final Pricing pricing;
  final int? travelTimeMinutes;

  const OrderConfirmationDialog({
    super.key,
    required this.departureStation,
    required this.deliveryStation,
    required this.orderType,
    required this.receiverName,
    required this.receiverPhone,
    required this.notes,
    required this.pricing,
    this.travelTimeMinutes,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Package Icon
            Assets.icons.package.image(
              height: 140,
              fit: BoxFit.contain,
            ),

            AppGaps.gap16,

            // Order Summary Title
            Text(
              context.tr.orderSummary,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            AppGaps.gap16,

            // Order Details
            _buildOrderDetail(
              context,
              context.tr.departureStation,
              departureStation.name,
              Icons.location_on,
            ),
            AppGaps.gap12,
            _buildOrderDetail(
              context,
              context.tr.deliveryStation,
              deliveryStation.name,
              CupertinoIcons.bus,
            ),
            AppGaps.gap12,
            _buildOrderDetail(
              context,
              context.tr.receiverName,
              receiverName,
              Icons.person,
            ),
            AppGaps.gap12,
            _buildOrderDetail(
              context,
              context.tr.receiverPhone,
              receiverPhone,
              Icons.phone,
            ),
            AppGaps.gap12,
            _buildOrderDetail(
              context,
              context.tr.orderType,
              orderType.name,
              Icons.scale,
            ),
            if (notes.isNotEmpty) ...[
              AppGaps.gap12,
              _buildOrderDetail(
                context,
                context.tr.notes,
                notes,
                CupertinoIcons.doc_on_clipboard,
              ),
            ],
            if (travelTimeMinutes != null) ...[
              AppGaps.gap12,
              _buildOrderDetail(
                context,
                context.tr.estimatedTravelTime,
                '$travelTimeMinutes ${context.tr.minutes}',
                Icons.access_time,
              ),
            ],
            AppGaps.gap12,
            _buildOrderDetail(
              context,
              context.tr.price,
              '${pricing.price} ${context.tr.currency}',
              Icons.attach_money,
              isPrice: true,
            ),
            AppGaps.gap24,

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: Button(
                    label: context.tr.editData,
                    onPressed: () => Navigator.of(context).pop(false),
                    color: Colors.grey.shade300,
                    isWhiteText: false,
                    textColor: Colors.black87,
                  ),
                ),
                AppGaps.gap12,
                Expanded(
                  child: Button(
                    label: context.tr.confirmOrderCreation,
                    onPressed: () => Navigator.of(context).pop(true),
                    color: ColorManager.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderDetail(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool isPrice = false,
  }) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: isPrice
                ? ColorManager.primaryColor.withOpacity(0.1)
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 18,
            color: isPrice ? ColorManager.primaryColor : Colors.grey.shade600,
          ),
        ),
        AppGaps.gap12,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelMedium.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: isPrice ? FontWeight.bold : FontWeight.w500,
                  color: isPrice ? ColorManager.primaryColor : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
