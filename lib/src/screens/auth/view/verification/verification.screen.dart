import 'package:dropx/src/core/shared/widgets/app_bar/base_header.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pinput/pinput.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/providers/auth_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/loading/loading_widget.dart';

class VerificationScreen extends HookConsumerWidget {
  final String phone;
  final Map<String, dynamic> userData;

  const VerificationScreen({
    super.key,
    required this.phone,
    required this.userData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final pinController = useTextEditingController();
    final focusNode = useFocusNode();
    final isFormValid = useState(false);

    void onPinChanged(String pin) {
      isFormValid.value = pin.length == 6;
    }

    void verifyCode() {
      if (pinController.text.length != 6) return;

      authController.verifyCode(
        userData: userData,
        code: pinController.text,
      );
    }

    void resendCode() {
      // Handle resend code logic
      showToast(context.tr.verificationCodeSent);

      authController.resendCode(
        phone: phone,
      );
    }

    useEffect(() {
      pinController.dispose;
      focusNode.dispose;
      return null;
    }, []);

    const borderColor = ColorManager.primaryColor;

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: GoogleFonts.cairo(
        fontSize: 22,
        color: Colors.black87,
        fontWeight: FontWeight.bold,
      ),
      decoration: const BoxDecoration(),
    );

    final cursor = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 56,
          height: 3,
          decoration: BoxDecoration(
            color: borderColor,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );

    final preFilledWidget = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 56,
          height: 3,
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SingleChildScrollView(
          child: Column(
            children: [
              // Header
              BaseHeaderWidget(
                title: context.tr.verificationTitle,
                withBackButton: true,
              ),

              // Content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    AppGaps.gap24,

                    // Verification Message
                    Text(
                      context.tr.verificationMessage,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.labelLarge.copyWith(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),

                    AppGaps.gap8,

                    // Phone Number
                    Text(
                      phone,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.title.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorManager.primaryColor,
                      ),
                    ),

                    AppGaps.gap24,

                    // Enter Code Label
                    Text(
                      context.tr.enterVerificationCode,
                      style: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black,
                      ),
                    ),

                    AppGaps.gap16,

                    // PIN Input
                    Directionality(
                      textDirection: TextDirection.ltr,
                      child: Pinput(
                        length: 6,
                        pinAnimationType: PinAnimationType.slide,
                        controller: pinController,
                        focusNode: focusNode,
                        defaultPinTheme: defaultPinTheme,
                        showCursor: true,
                        cursor: cursor,
                        preFilledWidget: preFilledWidget,
                        onChanged: onPinChanged,
                        onCompleted: (pin) {
                          verifyCode();
                        },
                      ),
                    ),

                    AppGaps.gap24,

                    // Verify Button
                    SizedBox(
                      height: 50,
                      width: double.infinity,
                      child: Button(
                        onPressed:
                            isFormValid.value && !authController.isLoading
                                ? verifyCode
                                : null,
                        label: context.tr.verify,
                        isLoading: authController.isLoading,
                        loadingWidget: LoadingWidget(),
                      ),
                    ),

                    AppGaps.gap16,

                    // Resend Code
                    Row(
                      children: [
                        Text(
                          context.tr.didntReceiveCode,
                          style: AppTextStyles.labelMedium.copyWith(
                            color: Colors.black54,
                            fontSize: 14,
                          ),
                        ),
                        TextButton(
                          onPressed: resendCode,
                          child: Text(
                            context.tr.resendCode,
                            style: AppTextStyles.labelMedium.copyWith(
                              color: ColorManager.termsLinkColor,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),

                    AppGaps.gap16,
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
