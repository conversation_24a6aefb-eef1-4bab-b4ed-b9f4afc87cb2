import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:location/location.dart' hide LocationAccuracy;
import 'package:xr_helper/xr_helper.dart';

class LocationService {
  static const String _googleMapsApiKey =
      'AIzaSyADsR5HMYECI-t746a-fh1yj94SlIOWsJM';

  /// Get current user location
  static Future<Position?> getCurrentLocation() async {
    try {
      await Location().requestService();

      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled.');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception(
            'Location permissions are permanently denied, we cannot request permissions.');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      return position;
    } catch (e) {
      Log.e('Error getting location: $e');
      return null;
    }
  }

  /// Calculate travel time between two coordinates using Google Maps Distance Matrix API
  static Future<int?> calculateTravelTimeInMinutes({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
  }) async {
    try {
      final String url =
          'https://maps.googleapis.com/maps/api/distancematrix/json'
          '?origins=$fromLat,$fromLng'
          '&destinations=$toLat,$toLng'
          '&mode=driving'
          '&language=en'
          '&key=$_googleMapsApiKey';

      final networkService = NetworkApiServices();
      final data = await networkService.getResponse(url);

      if (data != null &&
          data['status'] == 'OK' &&
          data['rows'] != null &&
          data['rows'].isNotEmpty &&
          data['rows'][0]['elements'] != null &&
          data['rows'][0]['elements'].isNotEmpty) {
        final element = data['rows'][0]['elements'][0];

        if (element['status'] == 'OK' && element['duration'] != null) {
          final durationInSeconds = element['duration']['value'] as int;
          final durationInMinutes = (durationInSeconds / 60).round();
          return durationInMinutes;
        }
      }

      return null;
    } catch (e) {
      Log.e('Error calculating travel time: $e');
      return null;
    }
  }

  /// Calculate travel time from current location to a destination
  static Future<int?> calculateTravelTimeFromCurrentLocation(
    BuildContext context, {
    required double destinationLat,
    required double destinationLng,
  }) async {
    try {
      final currentPosition = await getCurrentLocation();
      if (currentPosition == null) {
        if (context.mounted) {
          showToast(context.tr.unableToGetCurrentLocation, isError: true);
        }
        return null;
      }

      return await calculateTravelTimeInMinutes(
        fromLat: currentPosition.latitude,
        fromLng: currentPosition.longitude,
        toLat: destinationLat,
        toLng: destinationLng,
      );
    } catch (e) {
      Log.e('Error calculating travel time from current location: $e');
      return null;
    }
  }

  /// Calculate distance between two coordinates in kilometers
  static double calculateDistance({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
  }) {
    return Geolocator.distanceBetween(fromLat, fromLng, toLat, toLng) / 1000;
  }
}
