import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import '../fields/base_search_sheet.dart';

class StationDropdown extends ConsumerWidget {
  final String label;
  final ValueNotifier<Station?> valueNotifier;
  final Widget? icon;

  const StationDropdown({
    super.key,
    required this.label,
    required this.valueNotifier,
    this.icon,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stationsAsync = ref.watch(getStationsFutureProvider);

    return stationsAsync.get<Widget>(
      data: (stations) {
        return BaseSearchSheet(
          label: label,
          selectedValue: valueNotifier.value,
          data: stations,
          icon: icon,
          itemModelAsName: (station) => (station as Station).name,
          onChanged: (selectedStation) {
            valueNotifier.value = selectedStation as Station?;
          },
        );
      },
      error: (error, stackTrace) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  context.tr.error,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.red,
                  ),
                ),
              ),
              const Icon(Icons.error, color: Colors.red),
            ],
          ),
        );
      },
    );
  }
}
